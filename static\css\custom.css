/* UX评估系统自定义样式 */

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

/* 页面加载动画 */
.page-enter {
    animation: slideInUp 0.6s ease-out;
}

/* 卡片悬停效果增强 */
.card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0,0,0,0.05);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

/* 按钮增强效果 */
.btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 指标卡片特殊效果 */
.metric-card {
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.metric-card:hover::before {
    transform: scaleX(1);
}

.metric-value {
    transition: all 0.3s ease;
}

.metric-card:hover .metric-value {
    animation: pulse 1s ease-in-out;
    color: var(--primary-color);
}

/* 进度条增强 */
.progress {
    position: relative;
    overflow: hidden;
}

.progress-bar {
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, .2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* 加载状态 */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

.status-indicator.running {
    background-color: var(--warning-color);
}

.status-indicator.completed {
    background-color: var(--success-color);
}

.status-indicator.error {
    background-color: var(--danger-color);
}

/* 图表容器增强 */
.chart-container {
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 10px;
    padding: 15px;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    padding: 2px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.chart-container:hover::before {
    opacity: 1;
}

/* 历史记录项增强 */
.history-item {
    transition: all 0.3s ease;
    position: relative;
}

.history-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.history-item:hover::before {
    transform: scaleY(1);
}

.history-item:hover {
    background-color: rgba(46, 134, 171, 0.05);
    border-color: var(--primary-color);
    padding-left: 20px;
}

/* 模态框增强 */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,0.1);
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,0.1);
    border-radius: 0 0 15px 15px;
}

/* 响应式增强 */
@media (max-width: 768px) {
    .metric-value {
        font-size: 1.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.25rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --card-bg: #2d2d2d;
        --text-color: #ffffff;
        --border-color: #404040;
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .card {
        background-color: var(--card-bg);
        border-color: var(--border-color);
    }
    
    .chart-container {
        background: linear-gradient(135deg, var(--card-bg) 0%, #3a3a3a 100%);
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .modal,
    footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
        page-break-inside: avoid;
    }
    
    .chart-container {
        background: white;
    }
}

/* 可访问性增强 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦点指示器 */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
    
    .btn-primary {
        background: #000;
        border-color: #000;
    }
    
    .text-muted {
        color: #666 !important;
    }
}
