{% extends "base.html" %}

{% block title %}UX评估系统 - 开始评估{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- 评估配置表单 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>配置UX评估
                </h5>
            </div>
            <div class="card-body">
                <form id="evaluationForm">
                    <!-- 应用选择 -->
                    <div class="mb-4">
                        <label for="appSelect" class="form-label">
                            <i class="fas fa-mobile-alt me-2"></i>选择应用
                        </label>
                        <select class="form-select" id="appSelect" name="app_name" required>
                            <option value="">请选择要评估的应用...</option>
                            {% for app in available_apps %}
                            <option value="{{ app }}">{{ app }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">从已配置的应用列表中选择要评估的移动应用</div>
                    </div>
                    
                    <!-- 任务描述 -->
                    <div class="mb-4">
                        <label for="taskDescription" class="form-label">
                            <i class="fas fa-tasks me-2"></i>任务描述
                        </label>
                        <textarea class="form-control" id="taskDescription" name="task_description" rows="4" 
                                  placeholder="请详细描述要评估的用户任务，例如：在应用中搜索并播放一首歌曲" required></textarea>
                        <div class="form-text">请详细描述用户需要完成的具体任务，这将影响评估的准确性</div>
                    </div>
                    
                    <!-- 评估模式 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-robot me-2"></i>评估模式
                        </label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="evaluation_mode" 
                                           id="autonomousMode" value="autonomous" checked>
                                    <label class="form-check-label" for="autonomousMode">
                                        <strong>自主探索</strong>
                                        <div class="text-muted small">AI自动探索应用并完成任务</div>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="evaluation_mode" 
                                           id="demonstrationMode" value="demonstration">
                                    <label class="form-check-label" for="demonstrationMode">
                                        <strong>人工演示</strong>
                                        <div class="text-muted small">基于人工演示进行评估</div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 高级选项 -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <label class="form-label mb-0">
                                <i class="fas fa-sliders-h me-2"></i>高级选项
                            </label>
                            <button type="button" class="btn btn-sm btn-outline-secondary" 
                                    data-bs-toggle="collapse" data-bs-target="#advancedOptions">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        
                        <div class="collapse mt-3" id="advancedOptions">
                            <div class="card card-body bg-light">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="maxSteps" class="form-label">最大步数</label>
                                            <input type="number" class="form-control" id="maxSteps" 
                                                   name="max_steps" value="20" min="5" max="50">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="timeout" class="form-label">超时时间(分钟)</label>
                                            <input type="number" class="form-control" id="timeout" 
                                                   name="timeout" value="10" min="1" max="30">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="generateReport" 
                                           name="generate_report" checked>
                                    <label class="form-check-label" for="generateReport">
                                        自动生成详细报告
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="startEvaluationBtn">
                            <i class="fas fa-play me-2"></i>开始评估
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 评估进度 -->
        <div class="card mt-4" id="progressCard" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-spinner fa-spin me-2"></i>评估进行中
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span id="progressText">准备开始...</span>
                        <span id="progressPercent">0%</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" id="progressBar" role="progressbar" 
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    评估过程可能需要几分钟时间，请耐心等待。系统正在分析应用的用户体验...
                </div>
            </div>
        </div>
        
        <!-- 评估完成 -->
        <div class="card mt-4" id="completedCard" style="display: none;">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>评估完成
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-3">UX评估已成功完成！您可以查看详细的评估结果和分析报告。</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ url_for('results_page') }}" class="btn btn-success">
                        <i class="fas fa-chart-bar me-2"></i>查看结果
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                        <i class="fas fa-redo me-2"></i>重新评估
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 错误提示 -->
        <div class="card mt-4" id="errorCard" style="display: none;">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>评估失败
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-3" id="errorMessage">评估过程中发生错误，请检查配置后重试。</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                        <i class="fas fa-redo me-2"></i>重新尝试
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let progressInterval;

document.getElementById('evaluationForm').addEventListener('submit', function(e) {
    e.preventDefault();
    startEvaluation();
});

function startEvaluation() {
    const formData = new FormData(document.getElementById('evaluationForm'));
    const data = Object.fromEntries(formData.entries());
    
    // 隐藏表单，显示进度
    document.getElementById('evaluationForm').parentElement.style.display = 'none';
    document.getElementById('progressCard').style.display = 'block';
    document.getElementById('completedCard').style.display = 'none';
    document.getElementById('errorCard').style.display = 'none';
    
    // 发送评估请求
    fetch('/api/start_evaluation', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            showError(result.error);
        } else {
            // 开始轮询进度
            progressInterval = setInterval(checkProgress, 1000);
        }
    })
    .catch(error => {
        showError('启动评估失败: ' + error.message);
    });
}

function checkProgress() {
    fetch('/api/evaluation_status')
    .then(response => response.json())
    .then(status => {
        updateProgress(status.progress, status.current_step);
        
        if (!status.running) {
            clearInterval(progressInterval);
            if (status.error) {
                showError(status.error);
            } else if (status.result) {
                showCompleted();
            }
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        showError('获取进度失败: ' + error.message);
    });
}

function updateProgress(progress, text) {
    document.getElementById('progressBar').style.width = progress + '%';
    document.getElementById('progressBar').setAttribute('aria-valuenow', progress);
    document.getElementById('progressPercent').textContent = progress + '%';
    document.getElementById('progressText').textContent = text;
}

function showCompleted() {
    document.getElementById('progressCard').style.display = 'none';
    document.getElementById('completedCard').style.display = 'block';
}

function showError(message) {
    document.getElementById('progressCard').style.display = 'none';
    document.getElementById('errorCard').style.display = 'block';
    document.getElementById('errorMessage').textContent = message;
}

function resetForm() {
    document.getElementById('evaluationForm').parentElement.style.display = 'block';
    document.getElementById('progressCard').style.display = 'none';
    document.getElementById('completedCard').style.display = 'none';
    document.getElementById('errorCard').style.display = 'none';
    
    if (progressInterval) {
        clearInterval(progressInterval);
    }
}
</script>
{% endblock %}
