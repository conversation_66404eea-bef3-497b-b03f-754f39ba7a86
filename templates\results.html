{% extends "base.html" %}

{% block title %}UX评估系统 - 评估结果{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 加载状态 -->
        <div class="card" id="loadingCard">
            <div class="card-body text-center py-5">
                <div class="loading-spinner mb-3"></div>
                <h5>正在加载评估结果...</h5>
                <p class="text-muted">请稍候，正在获取最新的评估数据</p>
            </div>
        </div>
        
        <!-- 无结果状态 -->
        <div class="card" id="noResultCard" style="display: none;">
            <div class="card-body text-center py-5">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h5>暂无评估结果</h5>
                <p class="text-muted mb-4">还没有进行过UX评估，请先开始一次评估</p>
                <a href="{{ url_for('evaluate_page') }}" class="btn btn-primary">
                    <i class="fas fa-play me-2"></i>开始评估
                </a>
            </div>
        </div>
        
        <!-- 评估结果 -->
        <div id="resultContent" style="display: none;">
            <!-- 结果概览 -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>评估结果概览
                        </h5>
                        <div>
                            <span class="badge bg-primary" id="evaluationTime"></span>
                            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="downloadReport()">
                                <i class="fas fa-download me-1"></i>下载报告
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-mobile-alt me-2"></i>应用信息</h6>
                            <p class="mb-1"><strong>应用名称:</strong> <span id="appName"></span></p>
                            <p class="mb-1"><strong>任务描述:</strong> <span id="taskDescription"></span></p>
                            <p class="mb-0"><strong>评估模式:</strong> <span id="evaluationMode"></span></p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-star me-2"></i>综合评分</h6>
                            <div class="d-flex align-items-center">
                                <div class="metric-value me-3" id="overallScore">0.0</div>
                                <div class="flex-grow-1">
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar" id="overallScoreBar" style="width: 0%"></div>
                                    </div>
                                    <small class="text-muted">满分 1.0</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 核心指标 -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card metric-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h6>任务完成率</h6>
                            <div class="metric-value" id="completionRate">0%</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card metric-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-route fa-2x text-primary mb-2"></i>
                            <h6>导航效率</h6>
                            <div class="metric-value" id="navigationEfficiency">0%</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card metric-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                            <h6>错误率</h6>
                            <div class="metric-value" id="errorRate">0%</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card metric-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-hand-pointer fa-2x text-info mb-2"></i>
                            <h6>交互流畅度</h6>
                            <div class="metric-value" id="interactionSmoothness">0%</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 详细分析图表 -->
            <div class="row mb-4">
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-radar me-2"></i>UX评分雷达图
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="radarChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>指标对比
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="barChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作分布和决策流程 -->
            <div class="row mb-4">
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>操作类型分布
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="pieChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-project-diagram me-2"></i>决策流程分析
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="decisionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 问题和建议 -->
            <div class="row mb-4">
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-danger text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-exclamation-circle me-2"></i>关键问题
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="criticalIssues">
                                <p class="text-muted">暂无关键问题</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>改进建议
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="recommendations">
                                <p class="text-muted">暂无改进建议</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细分析 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-microscope me-2"></i>详细分析报告
                    </h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="analysisAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="interfaceAnalysisHeader">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#interfaceAnalysis">
                                    <i class="fas fa-desktop me-2"></i>界面可用性分析
                                </button>
                            </h2>
                            <div id="interfaceAnalysis" class="accordion-collapse collapse show"
                                 data-bs-parent="#analysisAccordion">
                                <div class="accordion-body" id="interfaceAnalysisContent">
                                    <p class="text-muted">正在加载分析内容...</p>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="interactionAnalysisHeader">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#interactionAnalysisContent">
                                    <i class="fas fa-hand-pointer me-2"></i>交互效率分析
                                </button>
                            </h2>
                            <div id="interactionAnalysisContent" class="accordion-collapse collapse"
                                 data-bs-parent="#analysisAccordion">
                                <div class="accordion-body" id="interactionAnalysisBody">
                                    <p class="text-muted">正在加载分析内容...</p>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="accessibilityAnalysisHeader">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#accessibilityAnalysisContent">
                                    <i class="fas fa-universal-access me-2"></i>可访问性分析
                                </button>
                            </h2>
                            <div id="accessibilityAnalysisContent" class="accordion-collapse collapse"
                                 data-bs-parent="#analysisAccordion">
                                <div class="accordion-body" id="accessibilityAnalysisBody">
                                    <p class="text-muted">正在加载分析内容...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentResult = null;
let charts = {};

// 页面加载时获取最新结果
document.addEventListener('DOMContentLoaded', function() {
    loadLatestResult();
});

function loadLatestResult() {
    fetch('/api/latest_result')
    .then(response => {
        if (!response.ok) {
            throw new Error('No results available');
        }
        return response.json();
    })
    .then(data => {
        currentResult = data;
        displayResult(data);
    })
    .catch(error => {
        console.error('Error loading result:', error);
        showNoResult();
    });
}

function showNoResult() {
    document.getElementById('loadingCard').style.display = 'none';
    document.getElementById('noResultCard').style.display = 'block';
    document.getElementById('resultContent').style.display = 'none';
}

function displayResult(data) {
    document.getElementById('loadingCard').style.display = 'none';
    document.getElementById('noResultCard').style.display = 'none';
    document.getElementById('resultContent').style.display = 'block';

    // 基本信息
    document.getElementById('appName').textContent = data.app_name;
    document.getElementById('taskDescription').textContent = data.task_description;
    document.getElementById('evaluationMode').textContent = data.evaluation_mode === 'autonomous' ? '自主探索' : '人工演示';
    document.getElementById('evaluationTime').textContent = new Date(data.timestamp).toLocaleString();

    // 综合评分
    const overallScore = data.result.overall_ux_score || 0;
    document.getElementById('overallScore').textContent = overallScore.toFixed(2);
    document.getElementById('overallScoreBar').style.width = (overallScore * 100) + '%';

    // 核心指标
    const metrics = data.result.metrics;
    document.getElementById('completionRate').textContent = (metrics.task_completion_rate * 100).toFixed(1) + '%';
    document.getElementById('navigationEfficiency').textContent = (metrics.navigation_efficiency * 100).toFixed(1) + '%';
    document.getElementById('errorRate').textContent = (metrics.error_rate * 100).toFixed(1) + '%';
    document.getElementById('interactionSmoothness').textContent = (metrics.interaction_smoothness * 100).toFixed(1) + '%';

    // 创建图表
    createCharts(data);

    // 显示问题和建议
    displayIssuesAndRecommendations(data.result);

    // 显示详细分析
    displayDetailedAnalysis(data.result);
}

function createCharts(data) {
    const metrics = data.result.metrics;

    // 雷达图
    createRadarChart(data.result);

    // 柱状图
    createBarChart(metrics);

    // 饼图
    createPieChart(metrics.action_distribution);

    // 决策流程图
    createDecisionChart(metrics.decision_distribution);
}

function createRadarChart(result) {
    const ctx = document.getElementById('radarChart').getContext('2d');

    if (charts.radar) {
        charts.radar.destroy();
    }

    charts.radar = new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['可用性', '可访问性', '一致性', '用户体验', '任务完成'],
            datasets: [{
                label: 'UX评分',
                data: [
                    result.usability_score / 10,
                    result.accessibility_score / 10,
                    result.consistency_score / 10,
                    result.user_experience_score / 10,
                    result.metrics.task_completion_rate
                ],
                backgroundColor: 'rgba(46, 134, 171, 0.2)',
                borderColor: 'rgba(46, 134, 171, 1)',
                pointBackgroundColor: 'rgba(46, 134, 171, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(46, 134, 171, 1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 1
                }
            }
        }
    });
}

function createBarChart(metrics) {
    const ctx = document.getElementById('barChart').getContext('2d');

    if (charts.bar) {
        charts.bar.destroy();
    }

    charts.bar = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['任务完成率', '导航效率', '交互流畅度', '目标达成度'],
            datasets: [{
                label: '评分',
                data: [
                    metrics.task_completion_rate,
                    metrics.navigation_efficiency,
                    metrics.interaction_smoothness,
                    metrics.goal_achievement_score
                ],
                backgroundColor: [
                    'rgba(46, 134, 171, 0.8)',
                    'rgba(162, 59, 114, 0.8)',
                    'rgba(241, 143, 1, 0.8)',
                    'rgba(76, 175, 80, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 1
                }
            }
        }
    });
}

function createPieChart(actionDistribution) {
    const ctx = document.getElementById('pieChart').getContext('2d');

    if (charts.pie) {
        charts.pie.destroy();
    }

    const labels = Object.keys(actionDistribution);
    const data = Object.values(actionDistribution);

    charts.pie = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    'rgba(46, 134, 171, 0.8)',
                    'rgba(162, 59, 114, 0.8)',
                    'rgba(241, 143, 1, 0.8)',
                    'rgba(76, 175, 80, 0.8)',
                    'rgba(255, 152, 0, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function createDecisionChart(decisionDistribution) {
    const ctx = document.getElementById('decisionChart').getContext('2d');

    if (charts.decision) {
        charts.decision.destroy();
    }

    const labels = Object.keys(decisionDistribution);
    const data = Object.values(decisionDistribution);

    charts.decision = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    'rgba(76, 175, 80, 0.8)',
                    'rgba(33, 150, 243, 0.8)',
                    'rgba(255, 152, 0, 0.8)',
                    'rgba(244, 67, 54, 0.8)',
                    'rgba(156, 39, 176, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function displayIssuesAndRecommendations(result) {
    // 关键问题
    const issuesContainer = document.getElementById('criticalIssues');
    if (result.critical_issues && result.critical_issues.length > 0) {
        issuesContainer.innerHTML = result.critical_issues.map(issue =>
            `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>${issue}</div>`
        ).join('');
    } else {
        issuesContainer.innerHTML = '<p class="text-muted">未发现关键问题</p>';
    }

    // 改进建议
    const recommendationsContainer = document.getElementById('recommendations');
    if (result.recommendations && result.recommendations.length > 0) {
        recommendationsContainer.innerHTML = result.recommendations.map(rec =>
            `<div class="alert alert-success"><i class="fas fa-lightbulb me-2"></i>${rec}</div>`
        ).join('');
    } else {
        recommendationsContainer.innerHTML = '<p class="text-muted">暂无改进建议</p>';
    }
}

function displayDetailedAnalysis(result) {
    // 界面可用性分析
    const interfaceAnalysis = result.interface_analysis;
    document.getElementById('interfaceAnalysisContent').innerHTML = formatAnalysis(interfaceAnalysis);

    // 交互效率分析
    const interactionAnalysis = result.interaction_analysis;
    document.getElementById('interactionAnalysisBody').innerHTML = formatAnalysis(interactionAnalysis);

    // 可访问性分析
    const accessibilityAnalysis = result.accessibility_analysis;
    document.getElementById('accessibilityAnalysisBody').innerHTML = formatAnalysis(accessibilityAnalysis);
}

function formatAnalysis(analysis) {
    if (!analysis || typeof analysis !== 'object') {
        return '<p class="text-muted">暂无分析数据</p>';
    }

    let html = '';
    for (const [key, value] of Object.entries(analysis)) {
        if (typeof value === 'object') {
            html += `<h6>${key}</h6><pre class="bg-light p-2 rounded">${JSON.stringify(value, null, 2)}</pre>`;
        } else {
            html += `<p><strong>${key}:</strong> ${value}</p>`;
        }
    }

    return html || '<p class="text-muted">暂无分析数据</p>';
}

function downloadReport() {
    if (currentResult) {
        // 这里可以实现报告下载功能
        alert('报告下载功能开发中...');
    }
}
</script>
{% endblock %}
