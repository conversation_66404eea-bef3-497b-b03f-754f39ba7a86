import re
import os
from abc import abstractmethod
from typing import List
from http import HTTPStatus

import requests
import dashscope

from utils import print_with_color, encode_image


class BaseModel:
    def __init__(self):
        pass

    @abstractmethod
    def get_model_response(self, prompt: str, images: List[str]) -> (bool, str):
        pass


class OpenAIModel(BaseModel):
    def __init__(self, base_url: str, api_key: str, model: str, temperature: float, max_tokens: int):
        super().__init__()
        self.base_url = base_url
        self.api_key = api_key
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens

    def get_model_response(self, prompt: str, images: List[str]) -> (bool, str):
        print(f"🔍 OpenAIModel DEBUG: Received images list: {images}")
        content = [
            {
                "type": "text",
                "text": prompt
            }
        ]
        for i, img in enumerate(images):
            print(f"🔍 OpenAIModel DEBUG: Processing image {i}: '{img}'")
            print(f"🔍 OpenAIModel DEBUG: Image exists: {os.path.exists(img) if img else 'N/A (empty)'}")
            try:
                base64_img = encode_image(img)
                print(f"🔍 OpenAIModel DEBUG: Successfully encoded image {i}")
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_img}"
                    }
                })
            except Exception as e:
                print(f"🔍 OpenAIModel DEBUG: Error encoding image {i}: {type(e).__name__}: {str(e)}")
                raise e
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": content
                }
            ],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }
        response = requests.post(self.base_url, headers=headers, json=payload).json()
        if "error" not in response:
            usage = response["usage"]
            prompt_tokens = usage["prompt_tokens"]
            completion_tokens = usage["completion_tokens"]
            print_with_color(f"Request cost is "
                             f"${'{0:.2f}'.format(prompt_tokens / 1000 * 0.01 + completion_tokens / 1000 * 0.03)}",
                             "yellow")
        else:
            return False, response["error"]["message"]
        return True, response["choices"][0]["message"]["content"]


class QwenModel(BaseModel):
    def __init__(self, api_key: str, model: str):
        super().__init__()
        self.model = model
        dashscope.api_key = api_key

    def get_model_response(self, prompt: str, images: List[str]) -> (bool, str):
        print(f"🔍 QwenModel DEBUG: Received images list: {images}")
        content = [{
            "text": prompt
        }]
        for i, img in enumerate(images):
            print(f"🔍 QwenModel DEBUG: Processing image {i}: '{img}'")
            print(f"🔍 QwenModel DEBUG: Image exists: {os.path.exists(img) if img else 'N/A (empty)'}")
            img_path = f"file://{img}"
            print(f"🔍 QwenModel DEBUG: Constructed img_path: '{img_path}'")
            content.append({
                "image": img_path
            })
        print(f"🔍 QwenModel DEBUG: Final content: {content}")
        messages = [
            {
                "role": "user",
                "content": content
            }
        ]
        print(f"🔍 QwenModel DEBUG: About to call dashscope.MultiModalConversation.call")
        try:
            response = dashscope.MultiModalConversation.call(model=self.model, messages=messages)
            print(f"🔍 QwenModel DEBUG: Response status: {response.status_code}")
            if response.status_code == HTTPStatus.OK:
                return True, response.output.choices[0].message.content[0]["text"]
            else:
                print(f"🔍 QwenModel DEBUG: Error response: {response.message}")
                return False, response.message
        except Exception as e:
            print(f"🔍 QwenModel DEBUG: Exception in dashscope call: {type(e).__name__}: {str(e)}")
            raise e


def parse_explore_rsp(rsp):
    try:
        # 安全地提取各个字段，如果找不到则使用默认值
        observation_matches = re.findall(r"Observation: (.*?)$", rsp, re.MULTILINE)
        think_matches = re.findall(r"Thought: (.*?)$", rsp, re.MULTILINE)
        act_matches = re.findall(r"Action: (.*?)$", rsp, re.MULTILINE)
        last_act_matches = re.findall(r"Summary: (.*?)$", rsp, re.MULTILINE)

        if not observation_matches or not think_matches or not act_matches or not last_act_matches:
            print_with_color("ERROR: Missing required fields in model response", "red")
            print_with_color(f"Response: {rsp[:200]}...", "red")
            return ["ERROR"]

        observation = observation_matches[0]
        think = think_matches[0]
        act = act_matches[0]
        last_act = last_act_matches[0]
        print_with_color("Observation:", "yellow")
        print_with_color(observation, "magenta")
        print_with_color("Thought:", "yellow")
        print_with_color(think, "magenta")
        print_with_color("Action:", "yellow")
        print_with_color(act, "magenta")
        print_with_color("Summary:", "yellow")
        print_with_color(last_act, "magenta")
        if "FINISH" in act:
            return ["FINISH"]
        act_name = act.split("(")[0]
        if act_name == "tap":
            area = int(re.findall(r"tap\((.*?)\)", act)[0])
            return [act_name, area, last_act]
        elif act_name == "text":
            input_str = re.findall(r"text\((.*?)\)", act)[0][1:-1]
            return [act_name, input_str, last_act]
        elif act_name == "long_press":
            area = int(re.findall(r"long_press\((.*?)\)", act)[0])
            return [act_name, area, last_act]
        elif act_name == "swipe":
            params = re.findall(r"swipe\((.*?)\)", act)[0]
            area, swipe_dir, dist = params.split(",")
            area = int(area)
            swipe_dir = swipe_dir.strip()[1:-1]
            dist = dist.strip()[1:-1]
            return [act_name, area, swipe_dir, dist, last_act]
        elif act_name == "grid":
            return [act_name]
        else:
            print_with_color(f"ERROR: Undefined act {act_name}!", "red")
            return ["ERROR"]
    except Exception as e:
        print_with_color(f"ERROR: an exception occurs while parsing the model response: {e}", "red")
        print_with_color(rsp, "red")
        return ["ERROR"]


def parse_grid_rsp(rsp):
    try:
        observation = re.findall(r"Observation: (.*?)$", rsp, re.MULTILINE)[0]
        think = re.findall(r"Thought: (.*?)$", rsp, re.MULTILINE)[0]
        act = re.findall(r"Action: (.*?)$", rsp, re.MULTILINE)[0]
        last_act = re.findall(r"Summary: (.*?)$", rsp, re.MULTILINE)[0]
        print_with_color("Observation:", "yellow")
        print_with_color(observation, "magenta")
        print_with_color("Thought:", "yellow")
        print_with_color(think, "magenta")
        print_with_color("Action:", "yellow")
        print_with_color(act, "magenta")
        print_with_color("Summary:", "yellow")
        print_with_color(last_act, "magenta")
        if "FINISH" in act:
            return ["FINISH"]
        act_name = act.split("(")[0]
        if act_name == "tap":
            params = re.findall(r"tap\((.*?)\)", act)[0].split(",")
            area = int(params[0].strip())
            subarea = params[1].strip()[1:-1]
            return [act_name + "_grid", area, subarea, last_act]
        elif act_name == "long_press":
            params = re.findall(r"long_press\((.*?)\)", act)[0].split(",")
            area = int(params[0].strip())
            subarea = params[1].strip()[1:-1]
            return [act_name + "_grid", area, subarea, last_act]
        elif act_name == "swipe":
            params = re.findall(r"swipe\((.*?)\)", act)[0].split(",")
            start_area = int(params[0].strip())
            start_subarea = params[1].strip()[1:-1]
            end_area = int(params[2].strip())
            end_subarea = params[3].strip()[1:-1]
            return [act_name + "_grid", start_area, start_subarea, end_area, end_subarea, last_act]
        elif act_name == "grid":
            return [act_name]
        else:
            print_with_color(f"ERROR: Undefined act {act_name}!", "red")
            return ["ERROR"]
    except Exception as e:
        print_with_color(f"ERROR: an exception occurs while parsing the model response: {e}", "red")
        print_with_color(rsp, "red")
        return ["ERROR"]


def parse_reflect_rsp(rsp):
    try:
        # 安全地提取字段
        decision_matches = re.findall(r"Decision:\s*(.*?)$", rsp, re.MULTILINE)
        think_matches = re.findall(r"Thought:\s*(.*?)$", rsp, re.MULTILINE)

        if not decision_matches or not think_matches:
            print_with_color("ERROR: Missing Decision or Thought in model response", "red")
            print_with_color(f"Response: {rsp[:200]}...", "red")
            return ["ERROR"]

        decision = decision_matches[0]
        think = think_matches[0]

        # 清理decision字符串
        decision = decision.strip().upper()

        print_with_color("Decision:", "yellow")
        print_with_color(decision, "magenta")
        print_with_color("Thought:", "yellow")
        print_with_color(think, "magenta")

        if decision == "INEFFECTIVE":
            return [decision, think]
        elif decision in ["BACK", "CONTINUE", "SUCCESS"]:
            doc_matches = re.findall(r"Documentation:\s*(.*?)$", rsp, re.MULTILINE)
            if doc_matches:
                doc = doc_matches[0]
                print_with_color("Documentation:", "yellow")
                print_with_color(doc, "magenta")
                return [decision, think, doc]
            else:
                print_with_color("WARNING: Missing Documentation field, using default", "yellow")
                return [decision, think, "No documentation provided"]
        else:
            print_with_color(f"ERROR: Undefined decision {decision}!", "red")
            print_with_color(f"Expected: INEFFECTIVE, BACK, CONTINUE, or SUCCESS", "red")
            return ["ERROR"]
    except Exception as e:
        print_with_color(f"ERROR: an exception occurs while parsing the model response: {e}", "red")
        print_with_color(rsp, "red")
        return ["ERROR"]
