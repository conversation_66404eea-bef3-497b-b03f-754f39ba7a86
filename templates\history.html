{% extends "base.html" %}

{% block title %}UX评估系统 - 历史记录{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-history me-2"></i>评估历史记录
            </h2>
            <div>
                <button class="btn btn-outline-secondary" onclick="refreshHistory()">
                    <i class="fas fa-sync-alt me-1"></i>刷新
                </button>
                <a href="{{ url_for('evaluate_page') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>新建评估
                </a>
            </div>
        </div>
        
        <!-- 统计概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x text-primary mb-2"></i>
                        <h6>总评估次数</h6>
                        <div class="metric-value" id="totalEvaluations">{{ history|length }}</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-mobile-alt fa-2x text-success mb-2"></i>
                        <h6>评估应用数</h6>
                        <div class="metric-value" id="totalApps">0</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-star fa-2x text-warning mb-2"></i>
                        <h6>平均评分</h6>
                        <div class="metric-value" id="averageScore">0.0</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar fa-2x text-info mb-2"></i>
                        <h6>最近评估</h6>
                        <div class="metric-value" id="lastEvaluation" style="font-size: 1rem;">-</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 筛选和搜索 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="appFilter" class="form-label">按应用筛选</label>
                        <select class="form-select" id="appFilter" onchange="filterHistory()">
                            <option value="">所有应用</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="dateFilter" class="form-label">按日期筛选</label>
                        <select class="form-select" id="dateFilter" onchange="filterHistory()">
                            <option value="">所有时间</option>
                            <option value="today">今天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="searchInput" class="form-label">搜索任务</label>
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="搜索任务描述..." onkeyup="filterHistory()">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 历史记录列表 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-list me-2"></i>评估记录
                </h6>
            </div>
            <div class="card-body">
                <div id="historyList">
                    {% if history %}
                        {% for item in history %}
                        <div class="history-item border rounded p-3 mb-3" data-app="{{ item.app_name }}" 
                             data-date="{{ item.timestamp }}" data-task="{{ item.task_description }}">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-mobile-alt text-primary me-2"></i>
                                        <h6 class="mb-0">{{ item.app_name }}</h6>
                                        <span class="badge bg-secondary ms-2">{{ item.evaluation_mode }}</span>
                                    </div>
                                    <p class="text-muted mb-1">{{ item.task_description }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ item.timestamp }}
                                    </small>
                                </div>
                                <div class="col-md-2 text-center">
                                    <div class="metric-value text-primary">
                                        {{ "%.2f"|format(item.result.overall_ux_score) }}
                                    </div>
                                    <small class="text-muted">综合评分</small>
                                </div>
                                <div class="col-md-2 text-end">
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="viewResult({{ loop.index0 }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" 
                                                onclick="downloadReport({{ loop.index0 }})">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteResult({{ loop.index0 }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5>暂无评估记录</h5>
                            <p class="text-muted mb-4">还没有进行过UX评估</p>
                            <a href="{{ url_for('evaluate_page') }}" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>开始第一次评估
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- 分页 -->
        <nav aria-label="历史记录分页" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- 结果详情模态框 -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-bar me-2"></i>评估结果详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modalContent">
                    <div class="text-center py-5">
                        <div class="loading-spinner mb-3"></div>
                        <p>正在加载结果详情...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="downloadCurrentReport()">
                    <i class="fas fa-download me-1"></i>下载报告
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let historyData = [];
let filteredData = [];
let currentPage = 1;
let itemsPerPage = 10;
let currentResultIndex = -1;

document.addEventListener('DOMContentLoaded', function() {
    loadHistoryData();
    calculateStatistics();
});

function loadHistoryData() {
    fetch('/api/history')
    .then(response => response.json())
    .then(data => {
        historyData = data;
        filteredData = data;
        populateFilters();
        calculateStatistics();
        displayHistory();
    })
    .catch(error => {
        console.error('Error loading history:', error);
    });
}

function populateFilters() {
    const appFilter = document.getElementById('appFilter');
    const apps = [...new Set(historyData.map(item => item.app_name))];
    
    appFilter.innerHTML = '<option value="">所有应用</option>';
    apps.forEach(app => {
        appFilter.innerHTML += `<option value="${app}">${app}</option>`;
    });
}

function calculateStatistics() {
    const totalEvaluations = historyData.length;
    const totalApps = new Set(historyData.map(item => item.app_name)).size;
    const averageScore = totalEvaluations > 0 ? 
        historyData.reduce((sum, item) => sum + item.result.overall_ux_score, 0) / totalEvaluations : 0;
    const lastEvaluation = totalEvaluations > 0 ? 
        new Date(historyData[0].timestamp).toLocaleDateString() : '-';
    
    document.getElementById('totalEvaluations').textContent = totalEvaluations;
    document.getElementById('totalApps').textContent = totalApps;
    document.getElementById('averageScore').textContent = averageScore.toFixed(2);
    document.getElementById('lastEvaluation').textContent = lastEvaluation;
}

function filterHistory() {
    const appFilter = document.getElementById('appFilter').value;
    const dateFilter = document.getElementById('dateFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    
    filteredData = historyData.filter(item => {
        // 应用筛选
        if (appFilter && item.app_name !== appFilter) return false;
        
        // 日期筛选
        if (dateFilter) {
            const itemDate = new Date(item.timestamp);
            const now = new Date();
            
            switch (dateFilter) {
                case 'today':
                    if (itemDate.toDateString() !== now.toDateString()) return false;
                    break;
                case 'week':
                    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    if (itemDate < weekAgo) return false;
                    break;
                case 'month':
                    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    if (itemDate < monthAgo) return false;
                    break;
            }
        }
        
        // 搜索筛选
        if (searchInput && !item.task_description.toLowerCase().includes(searchInput)) return false;
        
        return true;
    });
    
    currentPage = 1;
    displayHistory();
}

function displayHistory() {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    const historyList = document.getElementById('historyList');
    
    if (pageData.length === 0) {
        historyList.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5>没有找到匹配的记录</h5>
                <p class="text-muted">请尝试调整筛选条件</p>
            </div>
        `;
        return;
    }
    
    historyList.innerHTML = pageData.map((item, index) => {
        const actualIndex = startIndex + index;
        return `
            <div class="history-item border rounded p-3 mb-3 fade-in">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-mobile-alt text-primary me-2"></i>
                            <h6 class="mb-0">${item.app_name}</h6>
                            <span class="badge bg-secondary ms-2">${item.evaluation_mode === 'autonomous' ? '自主探索' : '人工演示'}</span>
                        </div>
                        <p class="text-muted mb-1">${item.task_description}</p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            ${new Date(item.timestamp).toLocaleString()}
                        </small>
                    </div>
                    <div class="col-md-2 text-center">
                        <div class="metric-value text-primary">
                            ${item.result.overall_ux_score.toFixed(2)}
                        </div>
                        <small class="text-muted">综合评分</small>
                    </div>
                    <div class="col-md-2 text-end">
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewResult(${actualIndex})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="downloadReport(${actualIndex})">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteResult(${actualIndex})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    updatePagination();
}

function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage || i === 1 || i === totalPages || Math.abs(i - currentPage) <= 2) {
            paginationHTML += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
        </li>
    `;
    
    pagination.innerHTML = paginationHTML;
}

function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        displayHistory();
    }
}

function viewResult(index) {
    currentResultIndex = index;
    const result = historyData[index];
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('resultModal'));
    modal.show();
    
    // 加载结果详情
    loadResultDetails(result);
}

function loadResultDetails(result) {
    const modalContent = document.getElementById('modalContent');
    
    modalContent.innerHTML = `
        <div class="row mb-4">
            <div class="col-md-6">
                <h6><i class="fas fa-mobile-alt me-2"></i>应用信息</h6>
                <p class="mb-1"><strong>应用名称:</strong> ${result.app_name}</p>
                <p class="mb-1"><strong>任务描述:</strong> ${result.task_description}</p>
                <p class="mb-0"><strong>评估时间:</strong> ${new Date(result.timestamp).toLocaleString()}</p>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-star me-2"></i>综合评分</h6>
                <div class="d-flex align-items-center">
                    <div class="metric-value me-3">${result.result.overall_ux_score.toFixed(2)}</div>
                    <div class="flex-grow-1">
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar" style="width: ${result.result.overall_ux_score * 100}%"></div>
                        </div>
                        <small class="text-muted">满分 1.0</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h6>任务完成率</h6>
                        <div class="metric-value">${(result.result.metrics.task_completion_rate * 100).toFixed(1)}%</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-route fa-2x text-primary mb-2"></i>
                        <h6>导航效率</h6>
                        <div class="metric-value">${(result.result.metrics.navigation_efficiency * 100).toFixed(1)}%</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <h6>错误率</h6>
                        <div class="metric-value">${(result.result.metrics.error_rate * 100).toFixed(1)}%</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-hand-pointer fa-2x text-info mb-2"></i>
                        <h6>交互流畅度</h6>
                        <div class="metric-value">${(result.result.metrics.interaction_smoothness * 100).toFixed(1)}%</div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function downloadReport(index) {
    window.open(`/download_report/${index}`, '_blank');
}

function downloadCurrentReport() {
    if (currentResultIndex >= 0) {
        downloadReport(currentResultIndex);
    }
}

function deleteResult(index) {
    if (confirm('确定要删除这条评估记录吗？此操作不可撤销。')) {
        // 这里可以实现删除功能
        alert('删除功能开发中...');
    }
}

function refreshHistory() {
    loadHistoryData();
}
</script>
{% endblock %}
