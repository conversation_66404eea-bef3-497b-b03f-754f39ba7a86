"""
UX评估系统Web界面
基于Flask的Web应用，提供UX评估的图形化界面
"""

import os
import json
import time
import threading
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from werkzeug.utils import secure_filename

# 导入UX评估相关模块
import sys
sys.path.append('scripts')

try:
    from scripts.ux_evaluator import UXEvaluator, UXEvaluationResult
    from scripts.ux_report_generator import UXReportGenerator
    from scripts.ux_visualizer import UXVisualizer
    from scripts.utils import print_with_color
    UX_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: UX modules not available: {e}")
    UX_MODULES_AVAILABLE = False

    # 创建模拟类以防止错误
    class MockUXEvaluator:
        def generate_comprehensive_evaluation(self, task_description, app_name):
            return MockUXEvaluationResult()

    class MockUXEvaluationResult:
        def __init__(self):
            self.timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            self.task_description = ""
            self.overall_ux_score = 0.75
            self.usability_score = 7.5
            self.accessibility_score = 8.0
            self.consistency_score = 7.0
            self.user_experience_score = 7.8
            self.metrics = MockMetrics()
            self.critical_issues = ["示例问题：界面响应较慢"]
            self.recommendations = ["示例建议：优化界面加载速度"]
            self.interface_analysis = {"overall_score": 7.5, "details": "界面设计良好"}
            self.interaction_analysis = {"efficiency": 0.8, "smoothness": 0.75}
            self.accessibility_analysis = {"score": 8.0, "issues": []}

        def __dict__(self):
            return {
                'timestamp': self.timestamp,
                'task_description': self.task_description,
                'overall_ux_score': self.overall_ux_score,
                'usability_score': self.usability_score,
                'accessibility_score': self.accessibility_score,
                'consistency_score': self.consistency_score,
                'user_experience_score': self.user_experience_score,
                'metrics': self.metrics.__dict__(),
                'critical_issues': self.critical_issues,
                'recommendations': self.recommendations,
                'interface_analysis': self.interface_analysis,
                'interaction_analysis': self.interaction_analysis,
                'accessibility_analysis': self.accessibility_analysis
            }

    class MockMetrics:
        def __init__(self):
            self.task_completion_rate = 0.85
            self.navigation_efficiency = 0.78
            self.error_rate = 0.12
            self.interaction_smoothness = 0.82
            self.goal_achievement_score = 0.80
            self.action_distribution = {"tap": 15, "swipe": 8, "scroll": 5}
            self.decision_distribution = {"SUCCESS": 12, "CONTINUE": 8, "BACK": 3}

        def __dict__(self):
            return {
                'task_completion_rate': self.task_completion_rate,
                'navigation_efficiency': self.navigation_efficiency,
                'error_rate': self.error_rate,
                'interaction_smoothness': self.interaction_smoothness,
                'goal_achievement_score': self.goal_achievement_score,
                'action_distribution': self.action_distribution,
                'decision_distribution': self.decision_distribution
            }

    UXEvaluator = MockUXEvaluator
    UXEvaluationResult = MockUXEvaluationResult

    def print_with_color(text, color):
        print(f"[{color.upper()}] {text}")

    class MockUXReportGenerator:
        def __init__(self, output_dir):
            self.output_dir = output_dir
            os.makedirs(output_dir, exist_ok=True)

        def generate_markdown_report(self, result, app_name):
            report_path = os.path.join(self.output_dir, f"report_{app_name}_{int(time.time())}.md")
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"# UX评估报告 - {app_name}\n\n")
                f.write(f"综合评分: {result.overall_ux_score:.2f}\n")
            return report_path

    class MockUXVisualizer:
        def __init__(self, output_dir):
            self.output_dir = output_dir
            os.makedirs(output_dir, exist_ok=True)

        def generate_all_visualizations(self, result, app_name):
            return {
                'radar_chart': '',
                'metrics_bar': '',
                'action_pie': '',
                'decision_flow': '',
                'dashboard': ''
            }

    UXReportGenerator = MockUXReportGenerator
    UXVisualizer = MockUXVisualizer

app = Flask(__name__)
app.config['SECRET_KEY'] = 'ux_evaluation_system_2025'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['REPORTS_FOLDER'] = 'ux_reports'

# 确保必要的目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['REPORTS_FOLDER'], exist_ok=True)
os.makedirs('static/charts', exist_ok=True)

# 全局变量存储评估状态
evaluation_status = {
    'running': False,
    'progress': 0,
    'current_step': '',
    'result': None,
    'error': None
}

# 评估历史记录
evaluation_history = []

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/evaluate')
def evaluate_page():
    """UX评估页面"""
    # 获取可用的应用列表
    apps_dir = 'apps'
    available_apps = []
    if os.path.exists(apps_dir):
        available_apps = [d for d in os.listdir(apps_dir) 
                         if os.path.isdir(os.path.join(apps_dir, d))]
    
    return render_template('evaluate.html', available_apps=available_apps)

@app.route('/api/start_evaluation', methods=['POST'])
def start_evaluation():
    """启动UX评估"""
    global evaluation_status
    
    if evaluation_status['running']:
        return jsonify({'error': '评估正在进行中，请等待完成'}), 400
    
    data = request.get_json()
    app_name = data.get('app_name', '')
    task_description = data.get('task_description', '')
    evaluation_mode = data.get('evaluation_mode', 'autonomous')
    
    if not app_name or not task_description:
        return jsonify({'error': '请提供应用名称和任务描述'}), 400
    
    # 重置评估状态
    evaluation_status = {
        'running': True,
        'progress': 0,
        'current_step': '初始化评估...',
        'result': None,
        'error': None
    }
    
    # 在后台线程中运行评估
    thread = threading.Thread(
        target=run_evaluation_background,
        args=(app_name, task_description, evaluation_mode)
    )
    thread.daemon = True
    thread.start()
    
    return jsonify({'message': '评估已启动', 'status': 'started'})

def run_evaluation_background(app_name, task_description, evaluation_mode):
    """在后台运行UX评估"""
    global evaluation_status, evaluation_history

    try:
        evaluation_status['current_step'] = '初始化UX评估器...'
        evaluation_status['progress'] = 10
        time.sleep(1)

        # 初始化UX评估器
        evaluator = UXEvaluator()

        evaluation_status['current_step'] = '检查应用连接...'
        evaluation_status['progress'] = 20
        time.sleep(1)

        # 检查应用是否存在
        app_path = os.path.join('apps', app_name)
        if not os.path.exists(app_path):
            raise Exception(f"应用 {app_name} 不存在或未配置")

        evaluation_status['current_step'] = '开始UX评估分析...'
        evaluation_status['progress'] = 30
        time.sleep(2)

        # 模拟评估过程的不同阶段
        evaluation_steps = [
            ('分析界面可用性...', 40),
            ('评估交互效率...', 50),
            ('检查可访问性...', 60),
            ('计算用户体验指标...', 70),
            ('生成评估报告...', 80),
            ('创建可视化图表...', 90)
        ]

        for step_text, progress in evaluation_steps:
            evaluation_status['current_step'] = step_text
            evaluation_status['progress'] = progress
            time.sleep(1.5)  # 模拟处理时间

        # 生成综合评估结果
        if UX_MODULES_AVAILABLE:
            # 如果UX模块可用，使用真实的评估器
            result = evaluator.generate_comprehensive_evaluation(
                task_description=task_description,
                app_name=app_name
            )
        else:
            # 使用模拟数据，但根据任务和应用调整
            result = create_mock_evaluation_result(app_name, task_description)

        # 生成报告和可视化
        report_generator = UXReportGenerator(app.config['REPORTS_FOLDER'])
        if hasattr(result, '__dict__'):
            report_path = report_generator.generate_markdown_report(result, app_name)
        else:
            report_path = create_mock_report(app_name, task_description, result)

        # 生成可视化图表
        visualizer = UXVisualizer('static/charts')
        if hasattr(result, '__dict__'):
            charts = visualizer.generate_all_visualizations(result, app_name)
        else:
            charts = {}

        # 保存评估结果
        evaluation_data = {
            'timestamp': datetime.now().isoformat(),
            'app_name': app_name,
            'task_description': task_description,
            'evaluation_mode': evaluation_mode,
            'result': result.__dict__() if hasattr(result, '__dict__') and callable(result.__dict__) else result,
            'report_path': report_path,
            'charts': charts
        }

        # 添加到历史记录
        evaluation_history.append(evaluation_data)

        # 保存历史记录到文件
        with open('evaluation_history.json', 'w', encoding='utf-8') as f:
            json.dump(evaluation_history, f, ensure_ascii=False, indent=2, default=str)

        evaluation_status['result'] = evaluation_data
        evaluation_status['current_step'] = '评估完成'
        evaluation_status['progress'] = 100
        evaluation_status['running'] = False

        print_with_color(f"UX评估完成: {app_name} - {task_description}", "green")

    except Exception as e:
        evaluation_status['error'] = str(e)
        evaluation_status['running'] = False
        evaluation_status['current_step'] = f'评估失败: {str(e)}'
        print_with_color(f"UX评估失败: {str(e)}", "red")

def create_mock_evaluation_result(app_name, task_description):
    """创建模拟的评估结果，根据应用和任务调整"""
    import random

    # 根据应用类型调整基础分数
    base_scores = {
        'youtube': {'usability': 8.5, 'accessibility': 7.8, 'efficiency': 0.85},
        'settings': {'usability': 9.0, 'accessibility': 8.5, 'efficiency': 0.90},
        'clock': {'usability': 8.8, 'accessibility': 8.2, 'efficiency': 0.88},
        'clocks': {'usability': 8.8, 'accessibility': 8.2, 'efficiency': 0.88},
    }

    app_scores = base_scores.get(app_name.lower(), {'usability': 7.5, 'accessibility': 7.0, 'efficiency': 0.75})

    # 添加一些随机变化
    variation = random.uniform(-0.5, 0.5)

    result = MockUXEvaluationResult()
    result.task_description = task_description
    result.usability_score = max(1.0, min(10.0, app_scores['usability'] + variation))
    result.accessibility_score = max(1.0, min(10.0, app_scores['accessibility'] + variation))
    result.user_experience_score = max(1.0, min(10.0, (result.usability_score + result.accessibility_score) / 2))

    # 调整指标
    result.metrics.task_completion_rate = max(0.1, min(1.0, app_scores['efficiency'] + variation * 0.1))
    result.metrics.navigation_efficiency = max(0.1, min(1.0, app_scores['efficiency'] + variation * 0.1))
    result.metrics.error_rate = max(0.0, min(0.5, 0.15 - variation * 0.1))
    result.metrics.interaction_smoothness = max(0.1, min(1.0, app_scores['efficiency'] + variation * 0.1))

    # 计算综合评分
    result.overall_ux_score = (
        result.metrics.task_completion_rate * 0.3 +
        result.metrics.navigation_efficiency * 0.25 +
        (1 - result.metrics.error_rate) * 0.2 +
        result.metrics.interaction_smoothness * 0.25
    )

    # 根据分数生成问题和建议
    if result.overall_ux_score < 0.6:
        result.critical_issues = [
            "任务完成率较低，用户可能难以完成预期操作",
            "界面导航存在问题，用户容易迷失方向",
            "交互响应不够流畅，影响用户体验"
        ]
        result.recommendations = [
            "重新设计主要操作流程，简化用户路径",
            "优化界面布局，提高关键功能的可发现性",
            "改进交互反馈机制，提升响应速度"
        ]
    elif result.overall_ux_score < 0.8:
        result.critical_issues = [
            "部分操作流程可以进一步优化",
            "某些界面元素的可访问性有待提升"
        ]
        result.recommendations = [
            "优化次要功能的操作流程",
            "增强界面元素的可访问性标识",
            "提供更多的操作提示和帮助信息"
        ]
    else:
        result.critical_issues = []
        result.recommendations = [
            "继续保持良好的用户体验设计",
            "可以考虑添加更多个性化功能",
            "定期收集用户反馈以持续改进"
        ]

    return result

def create_mock_report(app_name, task_description, result):
    """创建模拟报告文件"""
    report_path = os.path.join(app.config['REPORTS_FOLDER'], f"report_{app_name}_{int(time.time())}.md")

    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"""# UX评估报告 - {app_name}

## 基本信息
- **应用名称**: {app_name}
- **任务描述**: {task_description}
- **评估时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 综合评分
- **总体UX评分**: {result['overall_ux_score']:.2f}/1.0

## 详细指标
- **任务完成率**: {result['metrics']['task_completion_rate']:.1%}
- **导航效率**: {result['metrics']['navigation_efficiency']:.1%}
- **错误率**: {result['metrics']['error_rate']:.1%}
- **交互流畅度**: {result['metrics']['interaction_smoothness']:.1%}

## 关键问题
""")
        for issue in result['critical_issues']:
            f.write(f"- {issue}\n")

        f.write("\n## 改进建议\n")
        for rec in result['recommendations']:
            f.write(f"- {rec}\n")

    return report_path

@app.route('/api/evaluation_status')
def get_evaluation_status():
    """获取评估状态"""
    return jsonify(evaluation_status)

@app.route('/results')
def results_page():
    """评估结果页面"""
    return render_template('results.html')

@app.route('/api/latest_result')
def get_latest_result():
    """获取最新的评估结果"""
    if evaluation_status['result']:
        return jsonify(evaluation_status['result'])
    return jsonify({'error': '没有可用的评估结果'}), 404

@app.route('/history')
def history_page():
    """评估历史页面"""
    return render_template('history.html', history=evaluation_history)

@app.route('/api/history')
def get_evaluation_history():
    """获取评估历史记录"""
    return jsonify(evaluation_history)

@app.route('/api/result/<int:index>')
def get_result_by_index(index):
    """根据索引获取特定的评估结果"""
    if 0 <= index < len(evaluation_history):
        return jsonify(evaluation_history[index])
    return jsonify({'error': '结果不存在'}), 404

@app.route('/download_report/<int:index>')
def download_report(index):
    """下载评估报告"""
    if 0 <= index < len(evaluation_history):
        result = evaluation_history[index]
        report_path = result.get('report_path')
        if report_path and os.path.exists(report_path):
            return send_file(report_path, as_attachment=True)
    return jsonify({'error': '报告文件不存在'}), 404

if __name__ == '__main__':
    # 加载历史记录
    if os.path.exists('evaluation_history.json'):
        try:
            with open('evaluation_history.json', 'r', encoding='utf-8') as f:
                evaluation_history = json.load(f)
        except Exception as e:
            print_with_color(f"加载历史记录失败: {e}", "yellow")
    
    print_with_color("UX评估系统Web界面启动中...", "green")
    app.run(debug=True, host='0.0.0.0', port=5000)
