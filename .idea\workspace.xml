<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6551a6bf-2536-4a07-be18-f84abbcfa270" name="变更" comment="">
      <change beforePath="$PROJECT_DIR$/config.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/config.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/learn.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/requirements.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/run.py" beforeDir="false" afterPath="$PROJECT_DIR$/run.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/and_controller.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/and_controller.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/config.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/model.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/model.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/self_explorer.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/self_explorer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/step_recorder.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/step_recorder.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/task_executor.py" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="30DgB1hZQp7jLPZDKWJECJJXidb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "D:/AppAgent2",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\AppAgent\scripts" />
      <recent name="D:\AppAgent" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\AppAgent\scripts" />
      <recent name="D:\AppAgent" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="6551a6bf-2536-4a07-be18-f84abbcfa270" name="变更" comment="" />
      <created>1753167417847</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753167417847</updated>
    </task>
    <servers />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/scripts/and_controller.py</url>
          <line>117</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/scripts/self_explorer.py</url>
          <line>154</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/scripts/eletest.py</url>
          <line>44</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/apitest.py</url>
          <line>15</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/scripts/self_explorer2.py</url>
          <line>284</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>